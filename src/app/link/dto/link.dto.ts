import { ApiProperty } from '@nestjs/swagger'
import { Transform, Type } from 'class-transformer'
import {
	IsDate,
	IsEnum,
	IsNotEmpty,
	IsNumber,
	IsOptional,
	IsString,
	IsUrl,
	Min,
} from 'class-validator'

export class GenerateLinkDto {
	@ApiProperty({
		description: 'The original URL to generate a link for',
		example: 'https://www.flipkart.com/product/123456',
	})
	@IsNotEmpty()
	@IsUrl({}, { message: 'Please provide a valid URL' })
	url: string
}

export class LinkResponseDto {
	@ApiProperty({
		description: 'The unique ID of the generated link',
		example: 'ICBLABCD1234',
	})
	linkId: string

	@ApiProperty({
		description: 'The original URL',
		example: 'https://www.flipkart.com/product/123456',
	})
	originalUrl: string

	@ApiProperty({
		description: 'The generated affiliate URL',
		example: 'https://dl.flipkart.com/dl/product/123456?affid=connectin',
	})
	generatedUrl: string

	@ApiProperty({
		description: 'The store name',
		example: 'Flipkart',
	})
	storeName: string

	@ApiProperty({
		description: 'The creation date',
		example: '2023-07-01T12:00:00.000Z',
	})
	createdAt: string

	@ApiProperty({
		description: 'The short URL',
		example: 'https://icashbk.in/ICBLABCD1234',
	})
	shortUrl: string
}

export class GetUserLinksResponseDto {
	@ApiProperty({
		description: 'List of user links',
		type: [LinkResponseDto],
	})
	links: LinkResponseDto[]

	@ApiProperty({
		description: 'Total count of links',
		example: 10,
	})
	totalCount: number
}

export class UserLinksQueryDto {
	@ApiProperty({ required: false, description: 'Filter by store ID' })
	@IsString()
	@IsOptional()
	storeId?: string

	@ApiProperty({ required: false, description: 'Filter by start date' })
	@IsDate()
	@IsOptional()
	@Type(() => Date)
	@Transform(({ value }) => new Date(value))
	startDate?: Date

	@ApiProperty({ required: false, description: 'Filter by end date' })
	@IsDate()
	@IsOptional()
	@Type(() => Date)
	@Transform(({ value }) => new Date(value))
	endDate?: Date

	@ApiProperty({ required: false, default: 1, description: 'Page number' })
	@IsNumber()
	@IsOptional()
	@Min(1)
	@Type(() => Number)
	page?: number = 1

	@ApiProperty({ required: false, default: 10, description: 'Items per page' })
	@IsNumber()
	@IsOptional()
	@Min(1)
	@Type(() => Number)
	limit?: number = 10
}

export class UserLinkResponseDto {
	@ApiProperty({ description: 'Link ID' })
	linkId: string

	@ApiProperty({ description: 'Short URL' })
	shortUrl: string

	@ApiProperty({ description: 'Store name' })
	storeName: string

	@ApiProperty({ description: 'Total number of clicks' })
	totalClicks: number

	@ApiProperty({
		description: 'Number of converted clicks (tracked or confirmed)',
	})
	convertedClicks: number

	@ApiProperty({ description: 'Total cashback earned' })
	totalCashbackEarned: number

	@ApiProperty({ description: 'Created at date' })
	createdAt: Date
}

export class PaginationResponseDto<T> {
	@ApiProperty({ description: 'List of items' })
	items: T[]

	@ApiProperty({ description: 'Total number of items' })
	total: number

	@ApiProperty({ description: 'Current page' })
	page: number

	@ApiProperty({ description: 'Number of pages' })
	pages: number

	@ApiProperty({ description: 'Items per page' })
	limit: number
}

export enum PeriodType {
	DAY = 'day',
	WEEK = 'week',
	MONTH = 'month',
	YEAR = 'year',
}

export class UserAnalyticsQueryDto {
	@ApiProperty({
		required: false,
		description: 'Filter by store ID',
		example: '60a12d7c9f47e32b54e3c59a',
	})
	@IsString()
	@IsOptional()
	storeId?: string

	@ApiProperty({
		required: false,
		description: 'Start date for analysis period',
		example: '2023-01-01',
	})
	@IsDate()
	@IsOptional()
	@Type(() => Date)
	@Transform(({ value }) => new Date(value))
	startDate?: Date

	@ApiProperty({
		required: false,
		description: 'End date for analysis period',
		example: '2023-12-31',
	})
	@IsDate()
	@IsOptional()
	@Type(() => Date)
	@Transform(({ value }) => new Date(value))
	endDate?: Date

	@ApiProperty({
		required: false,
		enum: PeriodType,
		description: 'Period type for comparison (day, week, month, year)',
		default: PeriodType.MONTH,
		example: 'month',
	})
	@IsEnum(PeriodType)
	@IsOptional()
	periodType?: PeriodType = PeriodType.MONTH
}

export class MetricWithChangeDto {
	@ApiProperty({ description: 'Current value' })
	value: number

	@ApiProperty({ description: 'Percentage change from previous period' })
	percentageChange: number
}
export class UserAnalyticsResponseDto {
	@ApiProperty({
		description: 'Total cashback earned in the current period',
		type: MetricWithChangeDto,
	})
	totalCashbackEarned: MetricWithChangeDto

	@ApiProperty({
		description: 'Conversion rate in the current period',
		type: MetricWithChangeDto,
	})
	conversionRate: MetricWithChangeDto

	@ApiProperty({
		description: 'Total clicks in the current period',
		type: MetricWithChangeDto,
	})
	totalClicks: MetricWithChangeDto
}
