import { Injectable, OnModuleInit } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Click, ClickDocument, User, UserDocument } from '@shared/entities'
import { Model } from 'mongoose'
import { BotService } from './bot.service'

@Injectable()
export class BotStatsService implements OnModuleInit {
	constructor(
		private readonly botService: BotService,
		@InjectModel(Click.name)
		private click: Model<ClickDocument>,
		@InjectModel(User.name)
		private user: Model<UserDocument>,
	) {}

	onModuleInit() {
		this.getBotStats()
	}

	/**
	 * Calculate total active users count
	 */
	private async getTotalActiveUsers(): Promise<number> {
		return await this.user.countDocuments({ status: 'active' })
	}

	/**
	 * Calculate newly registered users in a given date range
	 */
	private async getNewlyRegisteredUsers(
		startDate: Date,
		endDate: Date,
	): Promise<number> {
		return await this.user.countDocuments({
			dateRegistered: {
				$gte: startDate,
				$lte: endDate,
			},
		})
	}

	/**
	 * Calculate click counts with specific status in a given date range
	 */
	private async getClickCountsByStatus(
		status: string,
		startDate: Date,
		endDate: Date,
	): Promise<number> {
		return await this.click.countDocuments({
			status,
			createdAt: {
				$gte: startDate,
				$lte: endDate,
			},
		})
	}

	/**
	 * Format the stats into a readable message
	 */
	private formatStatsMessage(stats: {
		totalActiveUsers: number
		newUsersLast7Days: number
		newUsersLastMonth: number
		clickedToday: number
		clickedLast7Days: number
		trackedLast7Days: number
		trackedLast30Days: number
		confirmedLast6Months: number
	}): string {
		return `<b>📊 ICashback Stats 📊</b>

<b>👥 User Statistics:</b>
• Total Verified Users: <b>${stats.totalActiveUsers}</b>
• New Users (Last 7 Days): <b>${stats.newUsersLast7Days}</b>
• New Users (Last Month): <b>${stats.newUsersLastMonth}</b>

<b>🖱️ Click Statistics:</b>
• Clicked Today: <b>${stats.clickedToday}</b>
• Clicked (Last 7 Days): <b>${stats.clickedLast7Days}</b>

<b>📈 Tracking Statistics:</b>
• Tracked (Last 7 Days): <b>${stats.trackedLast7Days}</b>
• Tracked (Last 30 Days): <b>${stats.trackedLast30Days}</b>

<b>✅ Confirmation Statistics:</b>
• Confirmed (Last 6 Months): <b>${stats.confirmedLast6Months}</b>

<i>Last Updated: ${new Date().toLocaleString('en-US', { timeZone: 'Asia/Kolkata' })}</i>`
	}

	async getBotStats() {
		const bot = this.botService.getBot()

		bot.command('stats', async ctx => {
			console.log('Thread ID:', ctx.message?.message_thread_id)
			const loadingMessage = await ctx.reply('Fetching stats...', {
				// biome-ignore lint/style/useNamingConvention: <explanation>
				parse_mode: 'HTML',
				disable_notification: true,
			})

			try {
				// Get current date
				const now = new Date()

				// Calculate date ranges
				const today = new Date(now)
				today.setHours(0, 0, 0, 0)

				const last7Days = new Date(now)
				last7Days.setDate(now.getDate() - 7)

				const lastMonth = new Date(now)
				lastMonth.setMonth(now.getMonth() - 1)

				const last30Days = new Date(now)
				last30Days.setDate(now.getDate() - 30)

				const last6Months = new Date(now)
				last6Months.setMonth(now.getMonth() - 6)

				// Calculate all stats in parallel
				const [
					totalActiveUsers,
					newUsersLast7Days,
					newUsersLastMonth,
					clickedToday,
					clickedLast7Days,
					trackedLast7Days,
					trackedLast30Days,
					confirmedLast6Months,
				] = await Promise.all([
					this.getTotalActiveUsers(),
					this.getNewlyRegisteredUsers(last7Days, now),
					this.getNewlyRegisteredUsers(lastMonth, now),
					this.getClickCountsByStatus('clicked', today, now),
					this.getClickCountsByStatus('clicked', last7Days, now),
					this.getClickCountsByStatus('tracked', last7Days, now),
					this.getClickCountsByStatus('tracked', last30Days, now),
					this.getClickCountsByStatus('confirmed', last6Months, now),
				])

				// Format the stats message
				const statsMessage = this.formatStatsMessage({
					totalActiveUsers,
					newUsersLast7Days,
					newUsersLastMonth,
					clickedToday,
					clickedLast7Days,
					trackedLast7Days,
					trackedLast30Days,
					confirmedLast6Months,
				})

				// Delete the loading message and send the stats
				await ctx.api.deleteMessage(ctx.chat.id, loadingMessage.message_id)
				await ctx.reply(statsMessage, {
					// biome-ignore lint/style/useNamingConvention: <explanation>
					parse_mode: 'HTML',
					disable_notification: true,
				})
			} catch (error) {
				console.error('Error fetching bot stats:', error)
				await ctx.reply('Error fetching stats. Please try again later.', {
					parse_mode: 'HTML',
				})
			}
		})
	}
}
