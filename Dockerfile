###################
# BASE IMAGE
###################
FROM oven/bun:latest AS base
WORKDIR /usr/src/app
COPY package.json ./
COPY bun.lock ./


###################
# BUILD FOR LOCAL DEVELOPMENT
###################
FROM base AS development
RUN bun i
COPY . .
USER bun

###################
# BUILD FOR PRODUCTION
###################
FROM base AS build
RUN bun i
COPY . .
RUN bun run build

###################
# PRODUCTION
###################
FROM base AS production
COPY --from=build /usr/src/app/dist ./dist
COPY --from=build /usr/src/app/node_modules ./node_modules
# Copy tsconfig.json to ensure path mappings work correctly
COPY --from=build /usr/src/app/tsconfig.json ./
COPY package.json ./
# USER bun
# RUN mkdir -p uploads && \
#     chown -R bun:bun /usr/src/app/uploads && \
#     chmod -R 755 /usr/src/app/uploads
ENV NODE_ENV=production
CMD [ "bun", "run", "start:prod" ]
